const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON>er without exposing the entire object
contextBridge.exposeInMainWorld("electronAPI", {
  // Example: You can add methods here to communicate between main and renderer process
  // For now, we'll keep it minimal for security

  // Get app version
  getVersion: () => process.versions.electron,

  // Platform info
  getPlatform: () => process.platform,

  // Example of secure IPC communication (if needed in future)
  // sendMessage: (message) => ipcRenderer.invoke('send-message', message),
  // onMessage: (callback) => ipcRenderer.on('message', callback)
});

// Security: Remove node integration from window object
delete window.require;
delete window.exports;
delete window.module;

// Prevent access to Node.js APIs
window.eval = global.eval = function () {
  throw new Error("eval() is disabled for security reasons.");
};
