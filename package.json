{"name": "blinkit", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1", "swiper": "^11.1.9"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "electron": "^32.0.0", "electron-builder": "^25.0.0", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "react-router-dom": "^6.27.0", "tailwindcss": "^3.4.10", "vite": "^5.4.1", "wait-on": "^8.0.0"}}