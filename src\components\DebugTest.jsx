import React from 'react';

const DebugTest = () => {
  console.log('DebugTest component is rendering');
  
  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', margin: '20px' }}>
      <h1 style={{ color: 'red', fontSize: '24px' }}>DEBUG TEST COMPONENT</h1>
      <p>If you can see this, React is working!</p>
      
      <div style={{ marginTop: '20px' }}>
        <h2>Test Image Loading:</h2>
        <img 
          src="https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/assets/products/sliding_images/jpeg/79c14c88-da9f-482d-8393-38f65756da08.jpg?ts=1707564216"
          alt="Test Product"
          style={{ width: '200px', height: '200px', objectFit: 'cover' }}
          onLoad={() => console.log('Image loaded successfully')}
          onError={() => console.log('Image failed to load')}
        />
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h2>Test Data:</h2>
        <div style={{ backgroundColor: 'white', padding: '10px', border: '1px solid #ccc' }}>
          <p><strong>Product Name:</strong> Harvest Gold Hearty Brown Bread</p>
          <p><strong>Weight:</strong> 400g</p>
          <p><strong>Price:</strong> ₹50</p>
        </div>
      </div>
    </div>
  );
};

export default DebugTest;
